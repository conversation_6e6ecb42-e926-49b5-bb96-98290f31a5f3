import 'package:hia_sang_ma/models/user_model.dart';

enum ChatType {
  private,
  task,
  department,
  organization;

  String get displayName {
    switch (this) {
      case ChatType.private:
        return 'ส่วนตัว';
      case ChatType.task:
        return 'งาน';
      case ChatType.department:
        return 'แผนก';
      case ChatType.organization:
        return 'องค์กร';
    }
  }

  static ChatType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'private':
        return ChatType.private;
      case 'task':
        return ChatType.task;
      case 'department':
        return ChatType.department;
      case 'organization':
        return ChatType.organization;
      default:
        return ChatType.private;
    }
  }
}

enum MessageType {
  text,
  image,
  file,
  sticker;

  static MessageType fromString(String value) {
    switch (value.toUpperCase()) {
      case 'TEXT':
        return MessageType.text;
      case 'IMAGE':
        return MessageType.image;
      case 'FILE':
        return MessageType.file;
      case 'STICKER':
        return MessageType.sticker;
      default:
        return MessageType.text;
    }
  }
}

enum MessageStatus {
  delivered,
  read,
  failed;

  static MessageStatus fromString(String value) {
    switch (value.toUpperCase()) {
      case 'DELIVERED':
        return MessageStatus.delivered;
      case 'READ':
        return MessageStatus.read;
      case 'FAILED':
        return MessageStatus.failed;
      default:
        return MessageStatus.delivered;
    }
  }
}

class ChatModel {
  final int id;
  final String? name;
  final ChatType chatType;
  final bool isActive;
  final bool isBot;
  final int? botDuration;
  final int? organizationId;
  final int? departmentId;
  final int? taskId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<ChatParticipant>? participants;
  final ChatMessage? lastMessage;
  final int unreadCount;
  final UserModel? user;

  ChatModel({
    required this.id,
    this.name,
    required this.chatType,
    this.isActive = true,
    this.isBot = false,
    this.botDuration,
    this.organizationId,
    this.departmentId,
    this.taskId,
    required this.createdAt,
    required this.updatedAt,
    this.participants,
    this.lastMessage,
    this.unreadCount = 0,
    this.user,
  });

  factory ChatModel.fromJson(Map<String, dynamic> json) {
    // Handle chatUsers from API response
    List<ChatParticipant>? participants;
    if (json['chatUsers'] != null) {
      participants = (json['chatUsers'] as List)
          .map((chatUser) => ChatParticipant.fromChatUser(chatUser))
          .toList();
    } else if (json['participants'] != null) {
      participants = (json['participants'] as List)
          .map((p) => ChatParticipant.fromJson(p))
          .toList();
    }

    // Handle last message from messages array
    ChatMessage? lastMessage;
    if (json['messages'] != null && (json['messages'] as List).isNotEmpty) {
      final messages = json['messages'] as List;
      // Get the most recent message
      final lastMessageData = messages.last;
      lastMessage = ChatMessage.fromJson(lastMessageData);
    } else if (json['lastMessage'] != null) {
      lastMessage = ChatMessage.fromJson(json['lastMessage']);
    }

    return ChatModel(
      id: _parseIntSafely(json['id']) ?? 0,
      name: json['name']?.toString(),
      chatType: ChatType.fromString(json['chatType']?.toString() ?? 'private'),
      isActive: json['isActive'] ?? true,
      isBot: json['isBot'] ?? false,
      botDuration: _parseIntSafely(json['botDuration']),
      organizationId: _parseIntSafely(json['organizationId']),
      departmentId: _parseIntSafely(json['departmentId']),
      taskId: _parseIntSafely(json['taskId']),
      createdAt:
          DateTime.tryParse(json['createdAt']?.toString() ?? '') ??
          DateTime.now(),
      updatedAt:
          DateTime.tryParse(json['updatedAt']?.toString() ?? '') ??
          DateTime.now(),
      participants: participants,
      lastMessage: lastMessage,
      unreadCount: _parseIntSafely(json['unreadCount']) ?? 0,
      user: json['user'] != null ? UserModel.fromJson(json['user']) : null,
    );
  }

  static int? _parseIntSafely(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value);
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'chatType': chatType.name,
      'isActive': isActive,
      'isBot': isBot,
      'botDuration': botDuration,
      'organizationId': organizationId,
      'departmentId': departmentId,
      'taskId': taskId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'participants': participants?.map((p) => p.toJson()).toList(),
      'lastMessage': lastMessage?.toJson(),
      'unreadCount': unreadCount,
      'user': user?.toJson(),
    };
  }

  String get displayName {
    // If chat has a custom name, use it first
    if (name != null && name!.trim().isNotEmpty) {
      return name!.trim();
    }

    switch (chatType) {
      case ChatType.private:
        // For private chats, prioritize direct user object first
        if (user != null) {
          final firstName = user!.firstName.trim();
          final lastName = user!.lastName.trim();
          
          if (firstName.isNotEmpty && lastName.isNotEmpty) {
            return '$firstName $lastName';
          } else if (firstName.isNotEmpty) {
            return firstName;
          } else if (lastName.isNotEmpty) {
            return lastName;
          }
        }
        
        // Fall back to participant names if no direct user object
        if (participants != null && participants!.isNotEmpty) {
          final participantNames = participants!
              .where((p) => 
                  p.user.firstName.trim().isNotEmpty || 
                  p.user.lastName.trim().isNotEmpty)
              .map((p) {
                final firstName = p.user.firstName.trim();
                final lastName = p.user.lastName.trim();
                
                if (firstName.isNotEmpty && lastName.isNotEmpty) {
                  return '$firstName $lastName';
                } else if (firstName.isNotEmpty) {
                  return firstName;
                } else if (lastName.isNotEmpty) {
                  return lastName;
                }
                return '';
              })
              .where((name) => name.isNotEmpty)
              .toList();
          
          if (participantNames.isNotEmpty) {
            // For private chat, show the first participant name only
            return participantNames.first;
          }
        }
        return 'แชทส่วนตัว #$id';

      case ChatType.task:
        return taskId != null ? 'แชทงาน #$taskId' : 'แชทงาน #$id';
        
      case ChatType.department:
        return departmentId != null ? 'แชทแผนก #$departmentId' : 'แชทแผนก #$id';
        
      case ChatType.organization:
        return organizationId != null ? 'แชทองค์กร #$organizationId' : 'แชทองค์กร #$id';
    }
  }

  @override
  String toString() {
    return 'ChatModel{id: $id, name: $name, chatType: $chatType, isActive: $isActive}';
  }
}

class ChatMessage {
  final int id;
  final int chatId;
  final int userId;
  final String content;
  final MessageType messageType;
  final MessageStatus messageStatus;
  final DateTime createdAt;
  final DateTime updatedAt;
  final UserModel? user;

  ChatMessage({
    required this.id,
    required this.chatId,
    required this.userId,
    required this.content,
    this.messageType = MessageType.text,
    this.messageStatus = MessageStatus.delivered,
    required this.createdAt,
    required this.updatedAt,
    this.user,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: _parseIntSafely(json['id']) ?? 0,
      chatId: _parseIntSafely(json['chatId']) ?? 0,
      userId: _parseIntSafely(json['userId']) ?? 0,
      content: json['content']?.toString() ?? '',
      messageType: MessageType.fromString(
        json['messageType']?.toString() ?? 'TEXT',
      ),
      messageStatus: MessageStatus.fromString(
        json['messageStatus']?.toString() ?? 'DELIVERED',
      ),
      createdAt:
          DateTime.tryParse(json['createdAt']?.toString() ?? '') ??
          DateTime.now(),
      updatedAt:
          DateTime.tryParse(json['updatedAt']?.toString() ?? '') ??
          DateTime.now(),
      user: json['user'] != null ? UserModel.fromJson(json['user']) : null,
    );
  }

  static int? _parseIntSafely(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value);
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'chatId': chatId,
      'userId': userId,
      'content': content,
      'messageType': messageType.name.toUpperCase(),
      'messageStatus': messageStatus.name.toUpperCase(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'user': user?.toJson(),
    };
  }

  String get timeDisplay {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${createdAt.day}/${createdAt.month}';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ชม.';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} นาที';
    } else {
      return 'เมื่อสักครู่';
    }
  }

  @override
  String toString() {
    return 'ChatMessage{id: $id, chatId: $chatId, userId: $userId, content: $content}';
  }
}

class ChatParticipant {
  final int id;
  final int chatId;
  final int userId;
  final DateTime joinedAt;
  final UserModel user;

  ChatParticipant({
    required this.id,
    required this.chatId,
    required this.userId,
    required this.joinedAt,
    required this.user,
  });

  factory ChatParticipant.fromJson(Map<String, dynamic> json) {
    return ChatParticipant(
      id: _parseIntSafely(json['id']) ?? 0,
      chatId: _parseIntSafely(json['chatId']) ?? 0,
      userId: _parseIntSafely(json['userId']) ?? 0,
      joinedAt:
          DateTime.tryParse(json['joinedAt']?.toString() ?? '') ??
          DateTime.now(),
      user: UserModel.fromJson(json['user'] ?? {}),
    );
  }

  factory ChatParticipant.fromChatUser(Map<String, dynamic> json) {
    return ChatParticipant(
      id: _parseIntSafely(json['id']) ?? 0,
      chatId: _parseIntSafely(json['chatId']) ?? 0,
      userId: _parseIntSafely(json['userId']) ?? 0,
      joinedAt:
          DateTime.tryParse(json['joinedAt']?.toString() ?? '') ??
          DateTime.now(),
      user: UserModel.fromJson(json['user'] ?? {}),
    );
  }

  static int? _parseIntSafely(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value);
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'chatId': chatId,
      'userId': userId,
      'joinedAt': joinedAt.toIso8601String(),
      'user': user.toJson(),
    };
  }

  @override
  String toString() {
    return 'ChatParticipant{id: $id, chatId: $chatId, userId: $userId, user: ${user.fullName}}';
  }
}
